import { format } from "date-fns";

export const parseTime = (timeString: string): number => {
  const [hours, minutes] = timeString.split(":").map(Number);
  return hours * 60 + minutes;
};

export const formatMinutesToHHMM = (totalMinutes: number): string => {
  // Handle minutes that exceed 24 hours or are negative
  const normalizedMinutes = ((totalMinutes % (24 * 60)) + 24 * 60) % (24 * 60);
  const hours = Math.floor(normalizedMinutes / 60);
  const minutes = normalizedMinutes % 60;
  return `${String(hours).padStart(2, "0")}:${String(minutes).padStart(2, "0")}`;
};

export const calculateDurationInMinutes = (
  start: string,
  end: string,
): number => {
  const startMinutes = parseTime(start);
  const endMinutes = parseTime(end);
  let duration = endMinutes - startMinutes;
  if (duration < 0) {
    duration += 24 * 60; // Handle overnight durations within a single day's context
  }
  return duration;
};

export const formatDuration = (minutes: number): string => {
  if (minutes === 0) return "0 min";
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  let result = "";
  if (hours > 0) {
    result += `${hours}h`;
  }
  if (remainingMinutes > 0) {
    result += `${remainingMinutes}min`;
  }
  return result.trim();
};

export const formatDateToYYYYMMDD = (date: Date): string => {
  return format(date, "yyyy-MM-dd");
};

export const formatHHMMToDate = (date: Date, hhmm: string): Date => {
  const [hours, minutes] = hhmm.split(":").map(Number);
  const newDate = new Date(date);
  newDate.setHours(hours, minutes, 0, 0);
  return newDate;
};

// Helper function to check if two time ranges overlap
export const doTimeRangesOverlap = (
  start1: string,
  end1: string,
  start2: string,
  end2: string,
): boolean => {
  const start1Minutes = parseTime(start1);
  const end1Minutes = parseTime(end1);
  const start2Minutes = parseTime(start2);
  const end2Minutes = parseTime(end2);

  return start1Minutes < end2Minutes && start2Minutes < end1Minutes;
};

// Helper function to adjust overlapping time slots
export const adjustOverlappingTimeSlots = (
  existingSlots: Array<{
    id: string;
    date: string;
    start: string;
    end: string;
    status: string;
    duration: string;
  }>,
  newSlot: {
    start: string;
    end: string;
    date: string;
  },
): Array<{
  id: string;
  date: string;
  start: string;
  end: string;
  status: string;
  duration: string;
}> => {
  const newStartMinutes = parseTime(newSlot.start);
  const newEndMinutes = parseTime(newSlot.end);

  return existingSlots
    .map((slot) => {
      // Only check slots on the same date
      if (slot.date !== newSlot.date) {
        return slot;
      }

      const slotStartMinutes = parseTime(slot.start);
      const slotEndMinutes = parseTime(slot.end);

      // Check if there's an overlap
      if (
        doTimeRangesOverlap(slot.start, slot.end, newSlot.start, newSlot.end)
      ) {
        let adjustedStart = slot.start;
        let adjustedEnd = slot.end;

        // Case 1: New slot completely contains the existing slot - remove existing slot
        if (
          newStartMinutes <= slotStartMinutes &&
          newEndMinutes >= slotEndMinutes
        ) {
          // Mark for removal by setting duration to 0
          return {
            ...slot,
            start: slot.start,
            end: slot.start,
            duration: "0 min",
          };
        }

        // Case 2: New slot overlaps with the beginning of existing slot
        if (
          newStartMinutes <= slotStartMinutes &&
          newEndMinutes > slotStartMinutes &&
          newEndMinutes < slotEndMinutes
        ) {
          adjustedStart = formatMinutesToHHMM(newEndMinutes);
        }

        // Case 3: New slot overlaps with the end of existing slot
        if (
          newStartMinutes > slotStartMinutes &&
          newStartMinutes < slotEndMinutes &&
          newEndMinutes >= slotEndMinutes
        ) {
          adjustedEnd = formatMinutesToHHMM(newStartMinutes);
        }

        // Case 4: New slot is completely inside existing slot - split existing slot
        if (
          newStartMinutes > slotStartMinutes &&
          newEndMinutes < slotEndMinutes
        ) {
          // For simplicity, we'll just adjust the end to the start of the new slot
          // In a more complex implementation, you might want to create two separate slots
          adjustedEnd = formatMinutesToHHMM(newStartMinutes);
        }

        // Calculate new duration
        const newDurationMinutes = calculateDurationInMinutes(
          adjustedStart,
          adjustedEnd,
        );
        const newDuration = formatDuration(newDurationMinutes);

        return {
          ...slot,
          start: adjustedStart,
          end: adjustedEnd,
          duration: newDuration,
        };
      }

      return slot;
    })
    .filter((slot) => slot.duration !== "0 min"); // Remove slots with 0 duration
};

export const STATUS_MAP: Record<
  string,
  { name: string; color: string; textColor?: string }
> = {
  F: { name: "Training", color: "bg-indigo-500" },
  P: { name: "Present in plant", color: "bg-green-600" },
  CTP: { name: "Planned TLO", color: "bg-teal-600" },
  "P in lan": { name: "Present in lan", color: "bg-green-800" },
  DN: { name: "National Travel", color: "bg-blue-400" },
  TE: { name: "Outside work", color: "bg-red-500" },
  CR: { name: "Vacation leave", color: "bg-indigo-500" },
  Ma: { name: "Sick leave", color: "bg-pink-500" },
  MT: { name: "Leave of maternity", color: "bg-teal-500" },
  AB: { name: "Absence", color: "bg-red-500" },
  CTN: { name: "Unplanned TLO", color: "bg-lime-500" },
  DI: { name: "International Travel", color: "bg-fuchsia-500" },
  AP: { name: "Layoff", color: "bg-rose-500" },
  TL: { name: "Legal authorization Absence", color: "bg-emerald-500" },
  "P in bus": { name: "Present in bus", color: "bg-violet-500" },
  Active: { name: "Active", color: "bg-red-600", textColor: "text-white" },
};
